#!/usr/bin/env python3
"""
face_recog_rt.py

Real-time face recognition for Raspberry Pi + Hailo/DeGirum + ChromaDB.

Modes:
  --mode ui       : show live video with bounding boxes/labels
  --mode console  : headless, record video for --duration seconds to --output

Camera backends:
  --camera auto   : try Picamera2, else fallback to OpenCV
  --camera picam  : force Picamera2
  --camera cv     : force OpenCV VideoCapture(0)

Depends:
  - degirum (DeGirum)
  - chromadb
  - opencv-python
  - picamera2 (optional for Pi Camera)
"""
from __future__ import annotations
import os
import time
import argparse
import logging
import signal
import threading
import queue
from typing import Optional, List, Tuple, Dict, Any

import numpy as np
import cv2

# optional libs
try:
    import degirum as dg
except Exception:
    dg = None

try:
    import chromadb
except Exception:
    chromadb = None

try:
    import supervision as sv
except Exception:
    sv = None

# Import intelligent recognition system
try:
    from intelligent_face_recognition import (
        IntelligentFaceRecog<PERSON><PERSON>,
        <PERSON>loRecognition<PERSON>ackend,
        SimpleCache,
        TrackState
    )
    INTELLIGENT_RECOGNITION_AVAILABLE = True
except Exception as e:
    # logger not available yet, will be defined later
    INTELLIGENT_RECOGNITION_AVAILABLE = False

# try picamera2 import - optional
try:
    from picamera2 import Picamera2, Preview
    PICAMERA2_AVAIL = True
except Exception:
    PICAMERA2_AVAIL = False

# logging
logging.basicConfig(level=logging.INFO, format="[%(levelname)s] %(message)s")
logger = logging.getLogger("face_recog_rt")

# constants
REF_KPS_112 = np.array([
    [38.2946, 51.6963],
    [73.5318, 51.5014],
    [56.0252, 71.7366],
    [41.5493, 92.3655],
    [70.7299, 92.2041]
], dtype=np.float32)

# default models (from your code)
DETECTOR_MODEL = "retinaface_mobilenet--736x1280_quant_hailort_hailo8l_1"
EMBED_MODEL = "arcface_mobilefacenet--112x112_quant_hailort_hailo8l_1"

# === utilities ===
def expand(p: Optional[str]) -> Optional[str]:
    return os.path.expanduser(p) if p else p

def safe_get_results(pred_out) -> List[dict]:
    if pred_out is None:
        return []
    if hasattr(pred_out, "results"):
        return list(getattr(pred_out, "results") or [])
    if isinstance(pred_out, (list, tuple)):
        return list(pred_out)
    if isinstance(pred_out, dict):
        return [pred_out]
    return []

def safe_extract_embedding(result: Dict[str, Any]) -> Optional[np.ndarray]:
    candidates = ['embedding', 'embeddings', 'vector', 'feat', 'features', 'features_vector']
    for k in result:
        if k.lower() in candidates:
            try:
                arr = np.asarray(result[k], dtype=float).flatten()
                if arr.size:
                    return arr
            except Exception:
                pass
    # fallback
    for k, v in result.items():
        try:
            arr = np.asarray(v, dtype=float).flatten()
            if arr.size:
                return arr
        except Exception:
            continue
    return None

def safe_parse_landmarks(raw_lm, w: int, h: int) -> Optional[np.ndarray]:
    if raw_lm is None:
        return None
    try:
        arr = np.asarray(raw_lm, dtype=float).flatten()
    except Exception:
        try:
            flat = []
            for item in raw_lm:
                flat.extend(np.asarray(item).flatten().tolist())
            arr = np.asarray(flat, dtype=float)
        except Exception:
            return None
    if arr.size == 10:
        arr = arr.reshape(5,2)
    else:
        try:
            arr = arr.reshape(5,2)
        except Exception:
            return None
    if arr.max() <= 1.01:
        arr[:,0] *= w
        arr[:,1] *= h
    return arr.astype(np.float32)

def align_face_to_112(frame_rgb: np.ndarray, landmarks: Optional[np.ndarray], bbox: Optional[List[int]]) -> np.ndarray:
    H,W = frame_rgb.shape[:2]
    if landmarks is not None:
        try:
            M, _ = cv2.estimateAffinePartial2D(np.array(landmarks, dtype=np.float32), REF_KPS_112, method=cv2.RANSAC, ransacReprojThreshold=5.0)
            if M is not None:
                return cv2.warpAffine(frame_rgb, M, (112,112), flags=cv2.INTER_LINEAR, borderMode=cv2.BORDER_REPLICATE)
        except Exception:
            pass
    if bbox is not None:
        try:
            x1,y1,x2,y2 = bbox
            x1 = max(0, int(round(x1))); y1 = max(0, int(round(y1)))
            x2 = min(W-1, int(round(x2))); y2 = min(H-1, int(round(y2)))
            if x2 > x1 and y2 > y1:
                margin = int(round(0.2 * max(x2-x1, y2-y1)))
                cx1 = max(0, x1 - margin); cy1 = max(0, y1 - margin)
                cx2 = min(W-1, x2 + margin); cy2 = min(H-1, y2 + margin)
                crop = frame_rgb[cy1:cy2, cx1:cx2]
                if crop.size != 0:
                    return cv2.resize(crop, (112,112), interpolation=cv2.INTER_LINEAR)
        except Exception:
            pass
    # center-crop fallback
    hmin = min(H,W); cy=H//2; cx=W//2; half=hmin//2
    crop = frame_rgb[max(0,cy-half):min(H,cy+half), max(0,cx-half):min(W,cx+half)]
    if crop.size == 0:
        return cv2.resize(frame_rgb, (112,112), interpolation=cv2.INTER_LINEAR)
    return cv2.resize(crop, (112,112), interpolation=cv2.INTER_LINEAR)

# === Supervision conversion utilities ===
def hailo_detections_to_supervision(hailo_detections: List[dict], class_names: Optional[List[str]] = None) -> Optional["sv.Detections"]:
    """
    Convert Hailo detection results to Supervision Detections format.

    Args:
        hailo_detections: List of dicts with keys: bbox, score, emb (optional)
        class_names: Optional list of class names for each detection

    Returns:
        sv.Detections object or None if supervision not available or no detections
    """
    if sv is None:
        logger.warning("Supervision library not available. Install with: pip install supervision")
        return None

    if not hailo_detections:
        # Return empty detections
        return sv.Detections.empty()

    # Extract data from Hailo detections
    xyxy_list = []
    confidence_list = []
    embeddings_list = []

    for det in hailo_detections:
        bbox = det.get("bbox")
        if bbox is not None and len(bbox) == 4:
            xyxy_list.append(bbox)  # [x1, y1, x2, y2]
            confidence_list.append(det.get("score", 0.0))

            # Store embedding if available
            emb = det.get("emb")
            if emb is not None:
                embeddings_list.append(emb)
            else:
                embeddings_list.append(None)

    if not xyxy_list:
        return sv.Detections.empty()

    # Convert to numpy arrays
    xyxy = np.array(xyxy_list, dtype=np.float32)
    confidence = np.array(confidence_list, dtype=np.float32)

    # For face detection, we'll use class_id=0 for all faces
    class_id = np.zeros(len(xyxy_list), dtype=int)

    # Create additional data dictionary
    data = {}

    # Add embeddings if available
    if any(emb is not None for emb in embeddings_list):
        data["embeddings"] = embeddings_list

    # Add class names if provided
    if class_names:
        if len(class_names) == len(xyxy_list):
            data["class_name"] = np.array(class_names)
        else:
            # Default to "face" for all detections
            data["class_name"] = np.array(["face"] * len(xyxy_list))
    else:
        data["class_name"] = np.array(["face"] * len(xyxy_list))

    # Create Supervision Detections object
    detections = sv.Detections(
        xyxy=xyxy,
        confidence=confidence,
        class_id=class_id,
        data=data
    )

    return detections

def supervision_detections_to_hailo(detections: "sv.Detections") -> List[dict]:
    """
    Convert Supervision Detections back to Hailo format for backward compatibility.

    Args:
        detections: sv.Detections object

    Returns:
        List of dicts with keys: bbox, score, emb (if available)
    """
    if detections is None or len(detections) == 0:
        return []

    hailo_detections = []
    embeddings = detections.data.get("embeddings", [None] * len(detections))

    for i in range(len(detections)):
        det_dict = {
            "bbox": detections.xyxy[i].tolist(),
            "score": float(detections.confidence[i])
        }

        # Add embedding if available
        if i < len(embeddings) and embeddings[i] is not None:
            det_dict["emb"] = embeddings[i]

        hailo_detections.append(det_dict)

    return hailo_detections

# === Chroma helper: try query, fallback to centroid scan ===
def open_chroma_client(db_path: str):
    if chromadb is None:
        raise RuntimeError("chromadb package not found. Install chromadb.")
    p = expand(db_path) or "db/face_embeddings_db"
    os.makedirs(p, exist_ok=True)
    client = chromadb.PersistentClient(path=p)
    try:
        col = client.get_or_create_collection(name="face_embeddings", metadata={"hnsw:space":"cosine"})
    except Exception:
        try:
            col = client.get_collection("face_embeddings")
        except Exception:
            col = client.create_collection("face_embeddings")
    return client, col

# try to query using collection.query, else fetch centroids manually
def db_query_topk(collection, embedding: List[float], k: int=3):
    """Return list of (metadata, distance) up to k results.
       Attempts collection.query(...) first, falls back to centroid scan.
    """
    # try query API
    try:
        resp = collection.query(query_embeddings=[embedding], n_results=k)
        # API shapes vary. Try common ones:
        metadatas = resp.get("metadatas") if isinstance(resp, dict) else None
        distances = resp.get("distances") if isinstance(resp, dict) else None
        if metadatas is not None and distances is not None:
            out = []
            for mlist, dlist in zip(metadatas, distances):
                # mlist is list of metas for one query
                for m,d in zip(mlist, dlist):
                    out.append((m,d))
            return out[:k]
    except Exception:
        pass

    # fallback: load centroids and compute cosine distance
    try:
        resp = collection.get(where={"type":"centroid"})
        metas = resp.get("metadatas", []) or []
        vecs = resp.get("embeddings", []) or []
        out = []
        qv = np.asarray(embedding, dtype=float)
        qv = qv / (np.linalg.norm(qv) + 1e-12)
        for m,v in zip(metas, vecs):
            vv = np.asarray(v, dtype=float)
            vv = vv / (np.linalg.norm(vv) + 1e-12)
            # cosine distance = 1 - dot
            d = 1.0 - float(np.dot(qv, vv))
            out.append((m,d))
        out.sort(key=lambda x: x[1])
        return out[:k]
    except Exception:
        return []

# === FaceRecognizer class ===
class FaceRecognizer:
    def __init__(self, zoo_path: str, detector_name: str = DETECTOR_MODEL, embed_name: str = EMBED_MODEL, db_path: str = "db/face_embeddings_db", top_k: int = 3, min_score: float = 0.5, max_distance: float = 0.47):
        self.zoo_path = expand(zoo_path)
        if dg is None:
            raise RuntimeError("degirum (dg) not available; install DeGirum SDK to use Hailo models")
        self.zoo = dg.connect(dg.LOCAL, self.zoo_path)
        self.detector = self.zoo.load_model(detector_name)
        self.embedder = self.zoo.load_model(embed_name)
        # try set RGB
        try:
            self.detector.input_numpy_colorspace = "RGB"
        except Exception:
            pass
        try:
            self.embedder.input_numpy_colorspace = "RGB"
        except Exception:
            pass
        self.client, self.collection = open_chroma_client(db_path)
        self.top_k = top_k
        self.min_score = min_score
        self.max_distance = max_distance  # Maximum cosine distance for valid recognition

        # Initialize intelligent recognition system
        self.intelligent_recognizer = None
        self._setup_intelligent_recognition()

    def _setup_intelligent_recognition(self):
        """Initialize the intelligent recognition system if available."""
        if INTELLIGENT_RECOGNITION_AVAILABLE:
            try:
                backend = HailoRecognitionBackend(self)
                cache = SimpleCache(
                    rerecognition_interval=30,  # Re-recognize every 30 frames
                    cache_timeout=60.0          # Cache expires after 60 seconds
                )
                self.intelligent_recognizer = IntelligentFaceRecognizer(
                    recognition_backend=backend,
                    cache_strategy=cache,
                    enable_optimization=True
                )
                logger.info("Intelligent recognition system initialized")
            except Exception as e:
                logger.warning("Failed to initialize intelligent recognition: %s", e)
                self.intelligent_recognizer = None
        else:
            logger.debug("Intelligent recognition not available")

    def detect_and_embed_intelligent(self, frame_rgb: np.ndarray, tracker_detections=None) -> Tuple[List[dict], Dict[str, Any]]:
        """
        Intelligent face recognition that only performs expensive operations when necessary.

        Args:
            frame_rgb: Input RGB frame
            tracker_detections: Optional supervision detections with tracker IDs

        Returns:
            Tuple of (detections_with_identity, performance_stats)
        """
        if self.intelligent_recognizer is not None:
            return self.intelligent_recognizer.process_frame(frame_rgb, tracker_detections)
        else:
            # Fallback to original method
            detections = self.detect_and_embed(frame_rgb)
            return detections, {}

    def detect_and_embed(self, frame_rgb: np.ndarray) -> List[dict]:
        """
        Run detector and embed on frame_rgb.
        Returns list of dicts: {bbox: [x1,y1,x2,y2], score: float, emb: np.ndarray, meta: optional}
        """
        out = []
        try:
            det_out = self.detector.predict(frame_rgb)
        except Exception as e:
            logger.warning("Detector predict failed: %s", e)
            return out
        results = safe_get_results(det_out)
        if not results:
            return out
        # filter by score, produce embeddings
        for res in results:
            score = float(res.get("score", 0.0))
            if score < self.min_score:
                continue
            raw_bbox = res.get("bbox") or res.get("box") or res.get("rectangle")
            bbox = None
            try:
                if raw_bbox is not None:
                    a = np.asarray(raw_bbox, dtype=float).flatten()
                    if a.size == 4:
                        x1,y1,x2,y2 = a.tolist()
                        if (x2 - x1) <= 1.0: x2 = x1 + x2
                        if (y2 - y1) <= 1.0: y2 = y1 + y2
                        bbox = [int(round(x1)), int(round(y1)), int(round(x2)), int(round(y2))]
            except Exception:
                bbox = None
            lm = safe_parse_landmarks(res.get("landmarks") or res.get("landmark") or res.get("keypoints"), frame_rgb.shape[1], frame_rgb.shape[0])
            aligned = align_face_to_112(frame_rgb, lm, bbox)
            try:
                emb_out = self.embedder.predict(aligned)
            except Exception as e:
                logger.debug("Embedder predict failed: %s", e)
                continue
            emb_res = safe_get_results(emb_out)
            if not emb_res:
                continue
            emb_vec = safe_extract_embedding(emb_res[0])
            if emb_vec is None:
                continue
            emb = np.asarray(emb_vec, dtype=float).flatten()
            nrm = np.linalg.norm(emb)
            if nrm <= 1e-8:
                continue
            emb = (emb / nrm).astype(float)
            out.append({"bbox": bbox, "score": score, "emb": emb})
        return out

    def detect_and_embed_supervision(self, frame_rgb: np.ndarray, include_labels: bool = True) -> Optional["sv.Detections"]:
        """
        Run detector and embed on frame_rgb, return results in Supervision format.

        Args:
            frame_rgb: Input RGB frame
            include_labels: Whether to include recognized person labels

        Returns:
            sv.Detections object or None if supervision not available
        """
        if sv is None:
            logger.warning("Supervision library not available. Use detect_and_embed() instead.")
            return None

        # Get detections in original format
        hailo_detections = self.detect_and_embed(frame_rgb)

        if not hailo_detections:
            return sv.Detections.empty()

        # Add recognition labels if requested
        class_names = []
        if include_labels:
            for det in hailo_detections:
                emb = det.get("emb")
                label = "unknown"
                if emb is not None:
                    matches = self.recognize_embeddings(emb)
                    if matches:
                        top_meta, top_d = matches[0]
                        # Check if distance is within acceptable threshold
                        if top_d <= self.max_distance:
                            # meta may be dict or list; handle dict
                            if isinstance(top_meta, dict):
                                name = top_meta.get("person") or top_meta.get("label") or "unknown"
                            else:
                                # sometimes metadata wrapped in list
                                try:
                                    name = top_meta[0].get("person") or "unknown"
                                except Exception:
                                    name = "unknown"
                            label = name
                class_names.append(label)
        else:
            class_names = ["face"] * len(hailo_detections)

        # Convert to supervision format
        return hailo_detections_to_supervision(hailo_detections, class_names)

    def recognize_embeddings(self, emb: List[float]) -> List[Tuple[dict,float]]:
        """Query DB for top-k matches. Returns list of (metadata, distance)."""
        try:
            results = db_query_topk(self.collection, emb.tolist() if isinstance(emb, np.ndarray) else emb, k=self.top_k)
            return results
        except Exception as e:
            logger.debug("DB query failed: %s", e)
            return []

    def cleanup(self):
        """Clean up resources used by the FaceRecognizer."""
        logger.info("Cleaning up FaceRecognizer resources...")
        try:
            # Close ChromaDB client connection
            if hasattr(self, 'client') and self.client:
                # ChromaDB doesn't have an explicit close method, but we can clear references
                self.client = None
                self.collection = None
        except Exception as e:
            logger.warning(f"Error cleaning up ChromaDB client: {e}")

        try:
            # Clean up DeGirum models
            if hasattr(self, 'detector') and self.detector:
                self.detector = None
            if hasattr(self, 'embedder') and self.embedder:
                self.embedder = None
            if hasattr(self, 'zoo') and self.zoo:
                self.zoo = None
        except Exception as e:
            logger.warning(f"Error cleaning up DeGirum models: {e}")

        logger.info("FaceRecognizer cleanup complete")

# === Camera wrappers ===
class CameraCapture:
    def __init__(self, backend: str = "auto", width: int = 640, height: int = 480, fps: int = 30):
        self.backend = backend
        self.width = width; self.height = height; self.fps = fps
        self.picam = None
        self.cap = None
        self.running = False
        self.use_picam2 = False
        if backend == "auto" and PICAMERA2_AVAIL:
            self.use_picam2 = True
        elif backend == "picam" and PICAMERA2_AVAIL:
            self.use_picam2 = True

    def start(self):
        if self.use_picam2:
            logger.info("Using Picamera2 backend")
            self.picam = Picamera2()
            config = self.picam.create_preview_configuration(main={"format":"RGB888","size":(self.width,self.height)})
            self.picam.configure(config)
            self.picam.start()
            self.running = True
        else:
            logger.info("Using OpenCV VideoCapture backend")
            self.cap = cv2.VideoCapture(0)
            # try set resolution
            self.cap.set(cv2.CAP_PROP_FRAME_WIDTH, self.width)
            self.cap.set(cv2.CAP_PROP_FRAME_HEIGHT, self.height)
            self.cap.set(cv2.CAP_PROP_FPS, self.fps)
            self.running = True

    def read(self) -> Optional[np.ndarray]:
        if not self.running:
            return None
        if self.use_picam2:
            frm = self.picam.capture_array()
            # Picamera2 returns RGB already
            return frm
        else:
            ok, frm = self.cap.read()
            if not ok:
                return None
            # convert BGR->RGB
            return cv2.cvtColor(frm, cv2.COLOR_BGR2RGB)

    def stop(self):
        if self.use_picam2 and self.picam:
            try:
                self.picam.stop()
            except Exception:
                pass
            self.picam = None
        if self.cap:
            try:
                self.cap.release()
            except Exception:
                pass
            self.cap = None
        self.running = False

# === threaded pipeline ===
def worker_loop(recognizer: FaceRecognizer, in_q: "queue.Queue[Tuple[int, np.ndarray]]", out_q: "queue.Queue[Tuple[int, np.ndarray, List[dict]]]", stop_evt: threading.Event, tracker=None, use_supervision: bool = False, enable_intelligent_recognition: bool = False):
    """
    Worker takes (frame_id, rgb_frame) from in_q, runs detect+embed+recognize,
    draws boxes/labels on a copy and puts (frame_id, annotated_rgb, detection_info) into out_q.

    Args:
        recognizer: FaceRecognizer instance
        in_q: Input queue with (frame_id, rgb_frame)
        out_q: Output queue with (frame_id, annotated_rgb, detection_info)
        stop_evt: Threading event to stop the worker
        tracker: Optional supervision tracker (e.g., sv.ByteTrack())
        use_supervision: Whether to use supervision format internally
    """
    logger.info("Worker thread started")
    while not stop_evt.is_set():
        try:
            item = in_q.get(timeout=0.2)
        except queue.Empty:
            continue

        # Check stop event again after getting item
        if stop_evt.is_set():
            # Put item back and exit
            try:
                in_q.put(item, timeout=0.1)
            except queue.Full:
                pass
            break
        fid, frame_rgb = item

        # Choose detection method based on flags
        if enable_intelligent_recognition and recognizer.intelligent_recognizer is not None:
            # Use intelligent recognition system
            if use_supervision and sv is not None:
                # Get detections without recognition first
                face_detections = recognizer.intelligent_recognizer.backend.detect_faces(frame_rgb)

                if face_detections:
                    # Convert to supervision format for tracking
                    sv_detections = hailo_detections_to_supervision(face_detections, ["face"] * len(face_detections))

                    if tracker is not None:
                        # Apply tracking
                        sv_detections = tracker.update_with_detections(sv_detections)

                    # Now use intelligent recognition with tracking info
                    detections, perf_stats = recognizer.detect_and_embed_intelligent(frame_rgb, sv_detections)


                    # Create detection_info from intelligent recognition results
                    detection_info = []
                    for det in detections:
                        info = {
                            "label": det.get("label", "unknown"),
                            "score": det.get("score", 0.0),
                            "distance": det.get("distance"),
                            "bbox": det.get("bbox"),
                            "tracker_id": det.get("tracker_id"),
                            "state": det.get("state", "unknown")
                        }
                        detection_info.append(info)

                    # Log performance stats periodically
                    if fid % 100 == 0 and perf_stats:
                        logger.info("Intelligent Recognition Stats: %s", perf_stats)
                else:
                    detections = []
                    detection_info = []
                    sv_detections = None
            else:
                # Use intelligent recognition without supervision tracking
                detections, perf_stats = recognizer.detect_and_embed_intelligent(frame_rgb, None)
                sv_detections = None

                # Create detection_info from intelligent recognition results
                detection_info = []
                for det in detections:
                    info = {
                        "label": det.get("label", "unknown"),
                        "score": det.get("score", 0.0),
                        "distance": det.get("distance"),
                        "bbox": det.get("bbox"),
                        "tracker_id": det.get("tracker_id"),
                        "state": det.get("state", "unknown")
                    }
                    detection_info.append(info)

                if fid % 100 == 0 and perf_stats:
                    logger.info("Intelligent Recognition Stats: %s", perf_stats)

        elif use_supervision and sv is not None:
            # Use supervision format with optional tracking (original behavior)
            sv_detections = recognizer.detect_and_embed_supervision(frame_rgb, include_labels=True)

            if sv_detections is not None and tracker is not None:
                # Apply tracking
                sv_detections = tracker.update_with_detections(sv_detections)

            # Convert back to original format for backward compatibility
            if sv_detections is not None:
                detections = supervision_detections_to_hailo(sv_detections)

                # Extract additional info for console output
                detection_info = []
                class_names = sv_detections.data.get("class_name", ["unknown"] * len(sv_detections))
                tracker_ids = getattr(sv_detections, 'tracker_id', None)

                for i, det in enumerate(detections):
                    info = {
                        "label": class_names[i] if i < len(class_names) else "unknown",
                        "score": det.get("score", 0.0),
                        "distance": None,  # Distance info not directly available in supervision format
                        "bbox": det.get("bbox"),
                        "tracker_id": tracker_ids[i] if tracker_ids is not None and i < len(tracker_ids) else None
                    }
                    detection_info.append(info)
            else:
                detections = []
                detection_info = []
        else:
            # Use original Hailo format
            detections = recognizer.detect_and_embed(frame_rgb)
            detection_info = []

            # Process detections for recognition and info extraction
            for det in detections:
                bbox = det.get("bbox")
                score = det.get("score", 0.0)
                emb = det.get("emb")
                label = "unknown"
                dist = None
                if emb is not None:
                    matches = recognizer.recognize_embeddings(emb)
                    if matches:
                        top_meta, top_d = matches[0]
                        # Check if distance is within acceptable threshold
                        if top_d <= recognizer.max_distance:
                            # meta may be dict or list; handle dict
                            if isinstance(top_meta, dict):
                                name = top_meta.get("person") or top_meta.get("label") or "unknown"
                            else:
                                # sometimes metadata wrapped in list
                                try:
                                    name = top_meta[0].get("person") or "unknown"
                                except Exception:
                                    name = "unknown"
                            label = f"{name}"
                            dist = top_d
                        else:
                            # Distance too high, treat as unknown
                            label = "unknown"
                            dist = top_d
                # collect detection info for console output
                detection_info.append({
                    "label": label,
                    "score": score,
                    "distance": dist,
                    "bbox": bbox,
                    "tracker_id": None  # No tracking in original format
                })

        # Draw annotations on frame
        annotated = frame_rgb.copy()



        if use_supervision and sv is not None and sv_detections is not None and len(sv_detections) > 0:
            # Use supervision annotators for better visualization
            try:
                box_annotator = sv.BoxAnnotator()
                label_annotator = sv.LabelAnnotator()

                # Create labels with tracking IDs if available
                labels = []
                class_names = sv_detections.data.get("class_name", ["face"] * len(sv_detections))
                tracker_ids = getattr(sv_detections, 'tracker_id', None)

                for i in range(len(sv_detections)):
                    class_name = class_names[i] if i < len(class_names) else "face"
                    confidence = sv_detections.confidence[i]

                    if tracker_ids is not None and i < len(tracker_ids) and tracker_ids[i] is not None:
                        label = f"#{tracker_ids[i]} {class_name} {confidence:.2f}"
                    else:
                        label = f"{class_name} {confidence:.2f}"
                    labels.append(label)

                # Apply annotations
                annotated = box_annotator.annotate(scene=annotated, detections=sv_detections)
                annotated = label_annotator.annotate(scene=annotated, detections=sv_detections, labels=labels)

            except Exception as e:
                logger.debug("Supervision annotation failed, falling back to OpenCV: %s", e)
                # Fall back to OpenCV drawing
                for det in detections:
                    bbox = det.get("bbox")
                    if bbox:
                        x1,y1,x2,y2 = bbox
                        cv2.rectangle(annotated, (x1,y1), (x2,y2), (0,255,0), 2)
        else:
            # Use OpenCV for drawing (original method)
            for i, det in enumerate(detections):
                bbox = det.get("bbox")
                if bbox:
                    x1,y1,x2,y2 = bbox
                    cv2.rectangle(annotated, (x1,y1), (x2,y2), (0,255,0), 2)

                    # Get label from detection_info
                    if i < len(detection_info):
                        info = detection_info[i]
                        label = info.get("label", "unknown")
                        score = info.get("score", 0.0)
                        dist = info.get("distance")
                        tracker_id = info.get("tracker_id")

                        if tracker_id is not None:
                            text = f"#{tracker_id} {label}"
                        elif dist is not None:
                            text = f"{label} {dist:.3f}"
                        else:
                            text = f"{label} {score:.2f}"

                        cv2.putText(annotated, text, (x1, max(0,y1-6)), cv2.FONT_HERSHEY_SIMPLEX, 0.45, (255,255,255), 1, cv2.LINE_AA)
        # push annotated with detection info
        try:
            out_q.put((fid, annotated, detection_info), timeout=0.5)
        except queue.Full:
            # If queue is full during shutdown, just skip
            if stop_evt.is_set():
                logger.debug("Output queue full during shutdown, skipping frame")
            pass

        try:
            in_q.task_done()
        except ValueError:
            # task_done() called more times than items in queue
            pass

    logger.info("Worker thread exiting gracefully")

# === console output helper ===
def format_detection_output(detections: List[dict], frame_id: int, timestamp: float) -> str:
    """Format detection results for console output."""
    if not detections:
        return f"[Frame {frame_id:06d}] [{time.strftime('%H:%M:%S', time.localtime(timestamp))}] No faces detected"

    lines = [f"[Frame {frame_id:06d}] [{time.strftime('%H:%M:%S', time.localtime(timestamp))}] {len(detections)} face(s) detected:"]
    for i, det in enumerate(detections, 1):
        label = det.get("label", "unknown")
        score = det.get("score", 0.0)
        distance = det.get("distance")
        bbox = det.get("bbox")

        if distance is not None:
            confidence_info = f"distance: {distance:.3f}"
        else:
            confidence_info = f"score: {score:.2f}"

        bbox_info = ""
        if bbox:
            x1, y1, x2, y2 = bbox
            bbox_info = f" at ({x1},{y1})-({x2},{y2})"

        lines.append(f"  {i}. {label} ({confidence_info}){bbox_info}")

    return "\n".join(lines)

def detections_changed(current: List[dict], previous: List[dict]) -> bool:
    """Check if detections have meaningfully changed."""
    if len(current) != len(previous):
        return True

    # Compare labels and significant confidence changes
    for curr, prev in zip(current, previous):
        if curr.get("label") != prev.get("label"):
            return True
        curr_dist = curr.get("distance")
        prev_dist = prev.get("distance")
        if curr_dist is not None and prev_dist is not None:
            if abs(curr_dist - prev_dist) > 0.1:  # Significant distance change
                return True
        else:
            curr_score = curr.get("score", 0.0)
            prev_score = prev.get("score", 0.0)
            if abs(curr_score - prev_score) > 0.1:  # Significant score change
                return True

    return False

# === main app ===
def main(args):
    # validate
    if args.mode not in ("ui","console"):
        raise SystemExit("mode must be ui or console")
    # camera
    cam_backend = args.camera
    if cam_backend == "auto":
        cam_backend = "picam" if PICAMERA2_AVAIL else "cv"
    cam_backend = "picam" if cam_backend in ("picam","picamera2") else "cv"

    # Initialize resources with proper error handling
    cap = None
    recognizer = None
    writer = None
    worker = None

    try:
        cap = CameraCapture(backend=cam_backend, width=args.width, height=args.height, fps=args.fps)
        cap.start()

        recognizer = FaceRecognizer(zoo_path=args.zoo_path, top_k=args.top_k, min_score=args.min_score, db_path=args.db_path, max_distance=args.max_distance)

        # Configure intelligent recognition if requested
        enable_intelligent_recognition = getattr(args, 'enable_intelligent_recognition', False)
        if enable_intelligent_recognition and recognizer.intelligent_recognizer is not None:
            # Update cache parameters if provided
            if hasattr(args, 'rerecognition_interval'):
                recognizer.intelligent_recognizer.cache.rerecognition_interval = args.rerecognition_interval
            if hasattr(args, 'cache_timeout'):
                recognizer.intelligent_recognizer.cache.cache_timeout = args.cache_timeout
            logger.info("Intelligent recognition enabled with interval=%d frames, timeout=%.1fs",
                       recognizer.intelligent_recognizer.cache.rerecognition_interval,
                       recognizer.intelligent_recognizer.cache.cache_timeout)

        # Initialize tracker if requested and supervision is available
        tracker = None
        use_supervision = getattr(args, 'enable_tracking', False) or getattr(args, 'use_supervision', False) or enable_intelligent_recognition

        if use_supervision and sv is not None:
            if getattr(args, 'enable_tracking', False):
                try:
                    # Initialize ByteTrack with configurable parameters
                    track_thresh = getattr(args, 'track_thresh', 0.25)
                    track_buffer = getattr(args, 'track_buffer', 30)
                    match_thresh = getattr(args, 'match_thresh', 0.8)
                    frame_rate = getattr(args, 'fps', 20)

                    tracker = sv.ByteTrack(
                        track_activation_threshold=track_thresh,
                        lost_track_buffer=track_buffer,
                        minimum_matching_threshold=match_thresh,
                        frame_rate=frame_rate
                    )
                    logger.info("ByteTrack tracker initialized with thresh=%.2f, buffer=%d, match=%.2f",
                               track_thresh, track_buffer, match_thresh)
                except Exception as e:
                    logger.warning("Failed to initialize tracker: %s", e)
                    tracker = None
            logger.info("Using Supervision format for detections")
        elif use_supervision:
            logger.warning("Supervision requested but not available. Install with: pip install supervision")
            use_supervision = False

        in_q: "queue.Queue[Tuple[int, np.ndarray]]" = queue.Queue(maxsize=4)
        out_q: "queue.Queue[Tuple[int, np.ndarray, List[dict]]]" = queue.Queue(maxsize=4)
        stop_evt = threading.Event()
        worker = threading.Thread(target=worker_loop, args=(recognizer, in_q, out_q, stop_evt, tracker, use_supervision, enable_intelligent_recognition), daemon=True)
        worker.start()

        # video writer if recording (only if not disabled)
        if args.output and not args.no_save_video:
            fourcc = cv2.VideoWriter_fourcc(*"mp4v")
            writer = cv2.VideoWriter(args.output, fourcc, args.fps, (args.width, args.height))
            logger.info("Video saving enabled: %s", args.output)
        elif args.no_save_video:
            logger.info("Video saving disabled for improved performance")

        frame_id = 0
        last_print = time.time()
        last_console_output = time.time()
        frames_processed = 0
        start_time = time.time()
        last_detections = []  # Track last detections for intelligent output

        # Global shutdown flag for graceful exit
        shutdown_requested = threading.Event()

        def handle_sigint(_signum, _frame):
            logger.info("Shutting down gracefully... (Press Ctrl+C again to force exit)")
            shutdown_requested.set()
            stop_evt.set()

        def handle_sigterm(_signum, _frame):
            logger.info("SIGTERM received, shutting down gracefully...")
            shutdown_requested.set()
            stop_evt.set()

        # Register signal handlers
        signal.signal(signal.SIGINT, handle_sigint)
        signal.signal(signal.SIGTERM, handle_sigterm)

        # main loop
        try:
            while not shutdown_requested.is_set():
                # Check duration limit (skip if duration is 0 for unlimited mode)
                if args.mode == "console" and args.duration > 0:
                    if time.time() - start_time >= args.duration:
                        logger.info("Duration reached, stopping.")
                        break

                # Check if shutdown was requested
                if shutdown_requested.is_set():
                    logger.info("Shutdown requested, exiting main loop...")
                    break

                frm = cap.read()
                if frm is None:
                    time.sleep(0.01)
                    continue
                # push to worker if queue free
                try:
                    in_q.put((frame_id, frm.copy()), timeout=0.01)
                except queue.Full:
                    # drop frame
                    pass

                # try to fetch annotated for display/record
                try:
                    fid_out, ann, detections = out_q.get_nowait()

                    # console output for detections
                    if args.console_output:
                        current_time = time.time()
                        # Output if interval passed or detections changed significantly
                        if (current_time - last_console_output >= args.output_interval or
                            detections_changed(detections, last_detections)):
                            output_text = format_detection_output(detections, fid_out, current_time)
                            print(output_text)
                            last_console_output = current_time
                            last_detections = detections.copy()

                    # convert RGB->BGR for display/writing
                    bgr = cv2.cvtColor(ann, cv2.COLOR_RGB2BGR)
                    if args.mode == "ui":
                        cv2.imshow("FaceRec", bgr)
                        key = cv2.waitKey(1) & 0xFF
                        if key == ord("q"):
                            logger.info("Quit pressed")
                            break
                        # Check for window close event (X button)
                        if cv2.getWindowProperty("FaceRec", cv2.WND_PROP_VISIBLE) < 1:
                            logger.info("Window closed, shutting down...")
                            break
                    if writer:
                        writer.write(bgr)
                    frames_processed += 1
                except queue.Empty:
                    pass

                frame_id += 1
                # periodic stats
                if time.time() - last_print >= 5.0:
                    elapsed = time.time() - start_time
                    fps = frames_processed / elapsed if elapsed > 0 else 0.0
                    logger.info("Elapsed %.1fs, processed frames: %d, approx FPS: %.2f", elapsed, frames_processed, fps)
                    last_print = time.time()

                # small sleep to be cooperative
                time.sleep(0.001)
        except KeyboardInterrupt:
            logger.info("KeyboardInterrupt caught, initiating graceful shutdown...")
            shutdown_requested.set()
            stop_evt.set()
        except Exception as e:
            logger.error(f"Unexpected error in main loop: {e}")
            shutdown_requested.set()
            stop_evt.set()

    except Exception as e:
        logger.error(f"Error during initialization: {e}")
    finally:
        logger.info("Starting cleanup process...")

        # Signal all threads to stop
        if 'stop_evt' in locals():
            stop_evt.set()
        if 'shutdown_requested' in locals():
            shutdown_requested.set()

        # Wait for worker thread to finish with timeout
        if worker and worker.is_alive():
            logger.info("Waiting for worker thread to finish...")
            worker.join(timeout=3.0)
            if worker.is_alive():
                logger.warning("Worker thread did not finish within timeout")

        # Stop camera capture
        if cap:
            logger.info("Stopping camera capture...")
            try:
                cap.stop()
            except Exception as e:
                logger.warning(f"Error stopping camera: {e}")

        # Clean up recognizer resources
        if recognizer:
            try:
                recognizer.cleanup()
            except Exception as e:
                logger.warning(f"Error cleaning up recognizer: {e}")

        # Release video writer
        if writer:
            logger.info("Releasing video writer...")
            try:
                writer.release()
            except Exception as e:
                logger.warning(f"Error releasing video writer: {e}")

        # Clean up OpenCV windows
        logger.info("Cleaning up OpenCV windows...")
        try:
            cv2.destroyAllWindows()
            # Give time for windows to close properly
            cv2.waitKey(1)
        except Exception as e:
            logger.warning(f"Error destroying OpenCV windows: {e}")

        # Clear queues to prevent hanging
        if 'in_q' in locals() and 'out_q' in locals():
            logger.info("Clearing queues...")
            try:
                while not in_q.empty():
                    try:
                        in_q.get_nowait()
                        in_q.task_done()
                    except queue.Empty:
                        break
            except Exception as e:
                logger.warning(f"Error clearing input queue: {e}")

            try:
                while not out_q.empty():
                    try:
                        out_q.get_nowait()
                    except queue.Empty:
                        break
            except Exception as e:
                logger.warning(f"Error clearing output queue: {e}")

        logger.info("Shutdown complete")

# === CLI ===
if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--mode", choices=["ui","console"], default="console")
    parser.add_argument("--camera", choices=["auto","picam","cv"], default="picam")
    parser.add_argument("--zoo_path", type=str, default="~/degirum-zoo")
    parser.add_argument("--db_path", type=str, default="db/face_embeddings_db")
    parser.add_argument("--output", type=str, default="output.mp4", help="Output mp4 file (console or ui)")
    parser.add_argument("--no_save_video", action="store_true", help="Disable video saving to improve performance")
    parser.add_argument("--console_output", action="store_true", help="Enable intelligent console detection output")
    parser.add_argument("--output_interval", type=float, default=2.0, help="Console output update interval in seconds")
    parser.add_argument("--duration", type=int, default=30, help="Duration in seconds for console mode (0 = unlimited)")
    parser.add_argument("--width", type=int, default=640)
    parser.add_argument("--height", type=int, default=480)
    parser.add_argument("--fps", type=int, default=20)
    parser.add_argument("--top_k", type=int, default=3)
    parser.add_argument("--min_score", type=float, default=0.7)
    parser.add_argument("--max_distance", type=float, default=0.47, help="Maximum cosine distance for valid face recognition (higher = less strict)")

    # Supervision and tracking options
    parser.add_argument("--enable_tracking", action="store_true", help="Enable object tracking using ByteTrack (requires supervision)")
    parser.add_argument("--use_supervision", action="store_true", help="Use supervision format for detections (better visualization)")
    parser.add_argument("--track_thresh", type=float, default=0.25, help="Detection confidence threshold for tracking")
    parser.add_argument("--track_buffer", type=int, default=30, help="Number of frames to keep lost tracks")
    parser.add_argument("--match_thresh", type=float, default=0.8, help="Matching threshold for associating detections with tracks")

    # Intelligent recognition options
    parser.add_argument("--enable_intelligent_recognition", action="store_true", help="Enable intelligent face recognition optimization")
    parser.add_argument("--rerecognition_interval", type=int, default=30, help="Frames between re-recognition for tracked faces")
    parser.add_argument("--cache_timeout", type=float, default=60.0, help="Cache timeout in seconds for face identities")

    args = parser.parse_args()

    main(args)
